# 数据库函数优化说明

## 概述

本文档说明了在集群监控数据聚合过程中如何使用数据库函数来优化最大值、最小值和平均值的更新操作。

## 核心优化点

### 1. 使用数据库函数进行值比较和计算

#### 最大值更新
```sql
-- 使用GREATEST函数自动选择最大值
max_value = GREATEST(max_value, VALUES(max_value))
```

#### 最小值更新
```sql
-- 使用LEAST函数自动选择最小值
min_value = LEAST(min_value, VALUES(min_value))
```

#### 平均值更新（加权平均）
```sql
-- 使用数学公式计算加权平均值
avg_value = (VALUES(avg_value) * VALUES(data_count) + avg_value * data_count) / (VALUES(data_count) + data_count)
```

### 2. 完整的INSERT ... ON DUPLICATE KEY UPDATE语句

```sql
INSERT INTO cluster_monitor_minute_data (
    cluster_code,
    metric_type,
    minute_time,
    avg_value,
    max_value,
    min_value,
    data_count,
    metric_unit,
    create_time
)
SELECT 
    cluster_code,
    metric_type,
    DATE_FORMAT(report_time, '%Y-%m-%d %H:%i:00') as minute_time,
    AVG(metric_value) as avg_value,
    MAX(metric_value) as max_value,
    MIN(metric_value) as min_value,
    COUNT(*) as data_count,
    MAX(metric_unit) as metric_unit,
    NOW() as create_time
FROM cluster_monitor_raw_data
WHERE report_time BETWEEN #{startTime} AND #{endTime}
GROUP BY cluster_code, metric_type, DATE_FORMAT(report_time, '%Y-%m-%d %H:%i:00')
ON DUPLICATE KEY UPDATE
    -- 使用数据库函数计算加权平均值
    avg_value = (VALUES(avg_value) * VALUES(data_count) + avg_value * data_count) / (VALUES(data_count) + data_count),
    -- 使用GREATEST函数更新最大值
    max_value = GREATEST(max_value, VALUES(max_value)),
    -- 使用LEAST函数更新最小值
    min_value = LEAST(min_value, VALUES(min_value)),
    -- 累加数据点数量
    data_count = data_count + VALUES(data_count),
    metric_unit = VALUES(metric_unit),
    update_time = NOW()
```

## 优化效果

### 1. 性能优势
- **原子操作**：所有计算在数据库层面完成，避免了应用层的多次查询
- **并发安全**：使用数据库锁机制，确保并发更新的数据一致性
- **减少网络开销**：减少应用与数据库之间的数据传输

### 2. 数据准确性
- **最大值保证**：GREATEST函数确保始终保留历史最大值
- **最小值保证**：LEAST函数确保始终保留历史最小值
- **平均值准确性**：加权平均算法确保聚合数据的统计准确性

### 3. 代码简洁性
- **减少业务逻辑**：复杂的数值比较逻辑由数据库函数处理
- **错误处理简化**：减少了应用层的异常处理复杂度

## 存储过程优化

### 优化前（使用游标）
```sql
-- 传统的游标方式，性能较差
DECLARE cur CURSOR FOR SELECT ...;
OPEN cur;
read_loop: LOOP
    FETCH cur INTO ...;
    -- 逐条处理
END LOOP;
CLOSE cur;
```

### 优化后（使用集合操作）
```sql
-- 使用集合操作，性能更好
INSERT INTO cluster_monitor_minute_data (...)
SELECT 
    cluster_code,
    metric_type,
    DATE_FORMAT(report_time, '%Y-%m-%d %H:%i:00') as minute_time,
    AVG(metric_value) as avg_value,
    MAX(metric_value) as max_value,
    MIN(metric_value) as min_value,
    COUNT(*) as data_count,
    MAX(metric_unit) as metric_unit,
    NOW() as create_time
FROM cluster_monitor_raw_data
WHERE report_time BETWEEN p_start_time AND p_end_time
GROUP BY cluster_code, metric_type, DATE_FORMAT(report_time, '%Y-%m-%d %H:%i:00')
ON DUPLICATE KEY UPDATE
    avg_value = (VALUES(avg_value) * VALUES(data_count) + avg_value * data_count) / (VALUES(data_count) + data_count),
    max_value = GREATEST(max_value, VALUES(max_value)),
    min_value = LEAST(min_value, VALUES(min_value)),
    data_count = data_count + VALUES(data_count),
    metric_unit = VALUES(metric_unit),
    update_time = NOW();
```

## 监控和日志

### 聚合日志表
```sql
CREATE TABLE `cluster_monitor_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `operation_type` varchar(32) NOT NULL COMMENT '操作类型：AGGREGATE,CLEAN等',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `affected_rows` int(11) DEFAULT '0' COMMENT '影响行数',
  `error_message` text COMMENT '错误信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_operation_create_time` (`operation_type`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='集群监控操作日志表';
```

### 性能监控
- 监控聚合任务执行时间
- 监控聚合数据的准确性
- 监控数据库函数的执行效率

## 最佳实践

1. **定期执行聚合**：每分钟执行一次，避免数据积压
2. **错误重试机制**：聚合失败时自动重试
3. **数据完整性检查**：定期检查聚合数据的完整性
4. **性能监控**：监控聚合任务的执行时间和资源消耗
5. **日志记录**：记录每次聚合操作的详细信息

## 总结

通过使用数据库函数（GREATEST、LEAST）和数学计算公式，我们实现了高效、准确的数据聚合操作。这种方法不仅提高了性能，还确保了数据的一致性和准确性，是处理大规模监控数据的最佳实践。

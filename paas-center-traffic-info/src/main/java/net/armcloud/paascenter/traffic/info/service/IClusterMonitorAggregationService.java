package net.armcloud.paascenter.traffic.info.service;

/**
 * 集群监控数据聚合服务接口
 *
 * <AUTHOR>
 */
public interface IClusterMonitorAggregationService {

    /**
     * 聚合指定时间范围的原始数据到分钟表
     *
     * @param minutesAgo 多少分钟前的数据
     * @return 聚合的数据条数
     */
    int aggregateRawDataToMinute(int minutesAgo);

    /**
     * 聚合最近的原始数据到分钟表（默认聚合2分钟前的数据）
     *
     * @return 聚合的数据条数
     */
    int aggregateRecentData();

    /**
     * 聚合指定时间范围的数据（支持批量聚合）
     *
     * @param startMinutesAgo 开始时间（多少分钟前）
     * @param endMinutesAgo 结束时间（多少分钟前）
     * @return 聚合的数据条数
     */
    int aggregateDataRange(int startMinutesAgo, int endMinutesAgo);

    /**
     * 检查并补充缺失的聚合数据
     *
     * @param hoursBack 检查多少小时前的数据
     * @return 补充的数据条数
     */
    int fillMissingAggregationData(int hoursBack);
}

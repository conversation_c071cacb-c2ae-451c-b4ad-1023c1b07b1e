package net.armcloud.paascenter.traffic.info.service.impl;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.traffic.info.mapper.paas.ClusterMonitorDataMapper;
import net.armcloud.paascenter.traffic.info.model.entity.ClusterMonitorMinuteData;
import net.armcloud.paascenter.traffic.info.service.IClusterMonitorAggregationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 集群监控数据聚合服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class ClusterMonitorAggregationServiceImpl implements IClusterMonitorAggregationService {

    @Resource
    private ClusterMonitorDataMapper clusterMonitorDataMapper;

    private static final String SYSTEM_USER = "system";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int aggregateRawDataToMinute(int minutesAgo) {
        try {
            Date endTime = DateUtil.offsetMinute(new Date(), -minutesAgo);
            Date startTime = DateUtil.offsetMinute(endTime, -1);

            log.info("开始聚合{}到{}的原始数据到分钟表", startTime, endTime);

            // 使用Java实现的聚合方法，已替代存储过程
            int aggregatedCount = clusterMonitorDataMapper.aggregateRawDataToMinute(startTime, endTime);

            log.info("聚合完成，处理了{}条记录", aggregatedCount);
            return aggregatedCount;

        } catch (Exception e) {
            log.error("聚合原始数据到分钟表失败", e);
            throw new RuntimeException("数据聚合失败", e);
        }
    }

    @Override
    public int aggregateRecentData() {
        return aggregateRawDataToMinute(2); // 聚合2分钟前的数据
    }

    /**
     * 聚合指定时间范围的数据（支持批量聚合）
     *
     * @param startMinutesAgo 开始时间（多少分钟前）
     * @param endMinutesAgo 结束时间（多少分钟前）
     * @return 聚合的数据条数
     */
    @Transactional(rollbackFor = Exception.class)
    public int aggregateDataRange(int startMinutesAgo, int endMinutesAgo) {
        try {
            Date endTime = DateUtil.offsetMinute(new Date(), -endMinutesAgo);
            Date startTime = DateUtil.offsetMinute(new Date(), -startMinutesAgo);

            log.info("批量聚合{}到{}的原始数据", startTime, endTime);

            int aggregatedCount = clusterMonitorDataMapper.aggregateRawDataToMinute(startTime, endTime);

            log.info("批量聚合完成，处理了{}条记录", aggregatedCount);
            return aggregatedCount;

        } catch (Exception e) {
            log.error("批量聚合数据失败", e);
            throw new RuntimeException("批量数据聚合失败", e);
        }
    }

    /**
     * 检查并补充缺失的聚合数据
     *
     * @param hoursBack 检查多少小时前的数据
     * @return 补充的数据条数
     */
    @Transactional(rollbackFor = Exception.class)
    public int fillMissingAggregationData(int hoursBack) {
        try {
            log.info("开始检查并补充{}小时内缺失的聚合数据", hoursBack);

            // 按小时分批聚合，避免一次性处理太多数据
            int totalAggregated = 0;
            for (int i = hoursBack; i > 0; i--) {
                int startMinutes = i * 60;
                int endMinutes = (i - 1) * 60;

                int count = aggregateDataRange(startMinutes, endMinutes);
                totalAggregated += count;

                // 避免长时间占用数据库连接
                if (i % 6 == 0) { // 每6小时休息一下
                    Thread.sleep(100);
                }
            }

            log.info("补充聚合数据完成，总共处理了{}条记录", totalAggregated);
            return totalAggregated;

        } catch (Exception e) {
            log.error("补充缺失聚合数据失败", e);
            throw new RuntimeException("补充聚合数据失败", e);
        }
    }
}

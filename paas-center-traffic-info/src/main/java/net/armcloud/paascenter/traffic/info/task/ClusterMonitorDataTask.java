package net.armcloud.paascenter.traffic.info.task;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.traffic.info.service.ICephPressureDataService;
import net.armcloud.paascenter.traffic.info.service.IClusterMonitorAggregationService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 集群监控数据处理定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ClusterMonitorDataTask {

    @Resource
    private ICephPressureDataService cephPressureDataService;

    @Resource
    private IClusterMonitorAggregationService aggregationService;

    /**
     * 每分钟执行数据聚合任务（已禁用，改为实时聚合）
     * 将原始数据聚合到分钟表
     * 注意：由于改为实时聚合，此定时任务已被禁用
     * 如需启用，请取消@Scheduled注释
     */
    // @Scheduled(cron = "0 * * * * ?")
    public void aggregateRawDataToMinute() {
        try {
            log.info("定时聚合任务已禁用，现在使用实时聚合");
            // 保留代码以备需要时启用
            // int aggregatedCount = aggregationService.aggregateRecentData();
            // if (aggregatedCount > 0) {
            //     log.info("数据聚合完成，聚合{}条记录", aggregatedCount);
            // }
        } catch (Exception e) {
            log.error("数据聚合任务执行失败", e);
        }
    }

    /**
     * 每小时执行一次，补充可能缺失的聚合数据
     * 检查过去2小时的数据，确保聚合完整性
     */
    @Scheduled(cron = "0 5 * * * ?")
    public void fillMissingAggregationData() {
        try {
            int filledCount = aggregationService.fillMissingAggregationData(2);
            if (filledCount > 0) {
                log.info("补充缺失聚合数据完成，补充{}条记录", filledCount);
            }
        } catch (Exception e) {
            log.error("补充缺失聚合数据任务执行失败", e);
        }
    }

    /**
     * 每天凌晨2点执行数据清理
     * 原始表保留1个月，分钟聚合表保留3个月
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanExpiredData() {
        log.info("开始清理集群监控过期数据");
        try {
            int deletedCount = cephPressureDataService.cleanExpiredData(0);
            log.info("集群监控过期数据清理完成，删除{}条记录", deletedCount);
        } catch (Exception e) {
            log.error("清理集群监控过期数据失败", e);
        }
    }
}

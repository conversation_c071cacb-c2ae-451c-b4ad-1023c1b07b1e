package net.armcloud.paascenter.traffic.info.service.impl;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.traffic.info.constant.MetricTypeConstant;
import net.armcloud.paascenter.traffic.info.internal.dto.CephPressureReportDTO;
import net.armcloud.paascenter.traffic.info.mapper.paas.ClusterMonitorDataMapper;
import net.armcloud.paascenter.traffic.info.model.dto.CephPressureChartDTO;
import net.armcloud.paascenter.traffic.info.model.dto.CephPressureQueryDTO;
import net.armcloud.paascenter.traffic.info.model.entity.ClusterMonitorRawData;
import net.armcloud.paascenter.traffic.info.service.ICephPressureDataService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Ceph压力数据服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class CephPressureDataServiceImpl implements ICephPressureDataService {

    @Resource
    private ClusterMonitorDataMapper clusterMonitorDataMapper;

    private static final int MAX_DATA_POINTS = 1000;
    private static final String SYSTEM_USER = "system";
    private static final int TIME_THRESHOLD_MINUTES = 30; // 30分钟阈值

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveCephPressureData(CephPressureReportDTO reportDTO) {
        try {
            if (reportDTO == null || reportDTO.getClusterCode() == null ||
                reportDTO.getMetrics() == null || reportDTO.getMetrics().getCephPressure() == null) {
                log.warn("Ceph压力数据为空或不完整: {}", reportDTO);
                return false;
            }

            Date reportTime = new Date();
            ClusterMonitorRawData data = new ClusterMonitorRawData();
            data.setClusterCode(reportDTO.getClusterCode());
            data.setMetricType(MetricTypeConstant.CEPH_PRESSURE);
            data.setMetricValue(reportDTO.getMetrics().getCephPressure());
            data.setMetricUnit("%");
            data.setReportTime(reportTime);
            data.setCreateTime(reportTime);
            data.setCreateBy(SYSTEM_USER);

            // 1. 保存原始数据
            int result = clusterMonitorDataMapper.insert(data);

            // 2. 实时更新分钟维度表
            if (result > 0) {
                int aggregateResult = clusterMonitorDataMapper.aggregateSingleDataToMinute(
                    reportDTO.getClusterCode(),
                    MetricTypeConstant.CEPH_PRESSURE,
                    reportDTO.getMetrics().getCephPressure(),
                    "%",
                    reportTime
                );
                log.info("实时聚合分钟数据: clusterCode={}, pressure={}, aggregateResult={}",
                        reportDTO.getClusterCode(), reportDTO.getMetrics().getCephPressure(), aggregateResult);
            }

            log.info("保存Ceph压力数据成功: clusterCode={}, pressure={}",
                    reportDTO.getClusterCode(), reportDTO.getMetrics().getCephPressure());

            return result > 0;
        } catch (Exception e) {
            log.error("保存Ceph压力数据失败", e);
            throw e;
        }
    }

    @Override
    public List<CephPressureChartDTO> getChartData(CephPressureQueryDTO queryDTO) {
        try {
            // 计算时间跨度（分钟）
            long diffMinutes = (queryDTO.getEndTime().getTime() - queryDTO.getStartTime().getTime()) / (1000 * 60);

            List<ClusterMonitorChartDataDTO> chartDataList;

            if (diffMinutes <= TIME_THRESHOLD_MINUTES) {
                // 时间跨度<=30分钟，查询原始表
                chartDataList = queryFromRawTable(queryDTO);
            } else {
                // 时间跨度>30分钟，查询分钟聚合表
                chartDataList = queryFromMinuteTable(queryDTO);
            }

            return convertToChartData(chartDataList);
        } catch (Exception e) {
            log.error("查询Ceph压力折线图数据失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 从原始表查询数据
     */
    private List<Map<String, Object>> queryFromRawTable(CephPressureQueryDTO queryDTO) {
        log.info("从原始表查询数据: clusterCode={}, timeRange={}分钟",
                queryDTO.getClusterCode(),
                (queryDTO.getEndTime().getTime() - queryDTO.getStartTime().getTime()) / (1000 * 60));

        return clusterMonitorDataMapper.selectMinuteDataFromRaw(
                queryDTO.getClusterCode(),
                MetricTypeConstant.CEPH_PRESSURE,
                queryDTO.getStartTime(),
                queryDTO.getEndTime());
    }

    /**
     * 从分钟聚合表查询数据
     */
    private List<Map<String, Object>> queryFromMinuteTable(CephPressureQueryDTO queryDTO) {
        // 先统计分钟聚合表中的数据量
        int dataCount = clusterMonitorDataMapper.countMinuteDataByTimeRange(
                queryDTO.getClusterCode(),
                MetricTypeConstant.CEPH_PRESSURE,
                queryDTO.getStartTime(),
                queryDTO.getEndTime());

        log.info("从分钟聚合表查询数据: clusterCode={}, dataCount={}",
                queryDTO.getClusterCode(), dataCount);

        if (dataCount <= MAX_DATA_POINTS) {
            // 数据量不超过1000，直接返回分钟维度数据
            return clusterMonitorDataMapper.selectMinuteDataFromAgg(
                    queryDTO.getClusterCode(),
                    MetricTypeConstant.CEPH_PRESSURE,
                    queryDTO.getStartTime(),
                    queryDTO.getEndTime());
        } else {
            // 数据量超过1000，进行聚合
            int intervalMinutes = calculateAggregationInterval(queryDTO.getStartTime(), queryDTO.getEndTime());
            log.info("数据量超过{}，使用{}分钟间隔聚合", MAX_DATA_POINTS, intervalMinutes);

            return clusterMonitorDataMapper.selectAggregatedDataFromAgg(
                    queryDTO.getClusterCode(),
                    MetricTypeConstant.CEPH_PRESSURE,
                    queryDTO.getStartTime(),
                    queryDTO.getEndTime(),
                    intervalMinutes);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanExpiredData(int retentionDays) {
        try {
            // 清理原始表数据（保留1个月）
            Date rawDataBeforeTime = DateUtil.offsetDay(new Date(), -30);
            int deletedRawCount = clusterMonitorDataMapper.deleteRawDataBeforeTime(rawDataBeforeTime);
            log.info("清理原始表30天前的数据，共删除{}条记录", deletedRawCount);

            // 清理分钟聚合表数据（保留3个月）
            Date minuteDataBeforeTime = DateUtil.offsetDay(new Date(), -90);
            int deletedMinuteCount = clusterMonitorDataMapper.deleteMinuteDataBeforeTime(minuteDataBeforeTime);
            log.info("清理分钟聚合表90天前的数据，共删除{}条记录", deletedMinuteCount);

            return deletedRawCount + deletedMinuteCount;
        } catch (Exception e) {
            log.error("清理过期监控数据失败", e);
            throw e;
        }
    }

    /**
     * 计算聚合间隔（分钟）
     */
    private int calculateAggregationInterval(Date startTime, Date endTime) {
        long diffMinutes = (endTime.getTime() - startTime.getTime()) / (1000 * 60);
        
        // 根据时间跨度计算合适的聚合间隔，确保数据点在1000左右
        int intervalMinutes = (int) Math.max(1, diffMinutes / MAX_DATA_POINTS);
        
        // 调整为合理的间隔值
        if (intervalMinutes <= 5) return 5;
        else if (intervalMinutes <= 10) return 10;
        else if (intervalMinutes <= 15) return 15;
        else if (intervalMinutes <= 30) return 30;
        else if (intervalMinutes <= 60) return 60;
        else return 120; // 最大2小时间隔
    }

    /**
     * 转换为图表数据格式
     */
    private List<CephPressureChartDTO> convertToChartData(List<Map<String, Object>> rawData) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        return rawData.stream().map(map -> {
            CephPressureChartDTO dto = new CephPressureChartDTO();
            
            try {
                String timeStr = (String) map.get("time_point");
                Date timePoint = sdf.parse(timeStr);
                dto.setTimePoint(timePoint);
                dto.setTimeStr(timeStr);
            } catch (ParseException e) {
                log.warn("时间格式解析失败: {}", map.get("time_point"));
                dto.setTimePoint(new Date());
                dto.setTimeStr(sdf.format(new Date()));
            }
            
            dto.setAvgPressure(((Number) map.get("avg_pressure")).doubleValue());
            dto.setMaxPressure(((Number) map.get("max_pressure")).doubleValue());
            dto.setMinPressure(((Number) map.get("min_pressure")).doubleValue());
            dto.setDataCount(((Number) map.get("data_count")).intValue());
            
            return dto;
        }).collect(Collectors.toList());
    }
}

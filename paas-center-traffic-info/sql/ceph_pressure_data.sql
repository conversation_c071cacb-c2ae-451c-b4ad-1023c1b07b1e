-- 集群监控原始数据表（支持多种指标）
CREATE TABLE `cluster_monitor_raw_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `cluster_code` varchar(64) NOT NULL COMMENT '集群编码',
  `metric_type` varchar(32) NOT NULL COMMENT '指标类型：ceph_pressure,write_rate,capacity_usage等',
  `metric_value` double NOT NULL COMMENT '指标值',
  `metric_unit` varchar(16) DEFAULT NULL COMMENT '指标单位：%,MB/s,GB等',
  `report_time` datetime NOT NULL COMMENT '上报时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT 'system' COMMENT '创建者',
  PRIMARY KEY (`id`),
  KEY `idx_cluster_metric_time` (`cluster_code`, `metric_type`, `report_time`),
  KEY `idx_report_time` (`report_time`),
  KEY `idx_metric_type` (`metric_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='集群监控原始数据表（保留1个月）'
PARTITION BY RANGE (TO_DAYS(report_time)) (
  PARTITION p202501 VALUES LESS THAN (TO_DAYS('2025-02-01')),
  PARTITION p202502 VALUES LESS THAN (TO_DAYS('2025-03-01')),
  PARTITION p202503 VALUES LESS THAN (TO_DAYS('2025-04-01')),
  PARTITION p202504 VALUES LESS THAN (TO_DAYS('2025-05-01')),
  PARTITION p202505 VALUES LESS THAN (TO_DAYS('2025-06-01')),
  PARTITION p202506 VALUES LESS THAN (TO_DAYS('2025-07-01')),
  PARTITION p202507 VALUES LESS THAN (TO_DAYS('2025-08-01')),
  PARTITION p202508 VALUES LESS THAN (TO_DAYS('2025-09-01')),
  PARTITION p202509 VALUES LESS THAN (TO_DAYS('2025-10-01')),
  PARTITION p202510 VALUES LESS THAN (TO_DAYS('2025-11-01')),
  PARTITION p202511 VALUES LESS THAN (TO_DAYS('2025-12-01')),
  PARTITION p202512 VALUES LESS THAN (TO_DAYS('2026-01-01')),
  PARTITION pmax VALUES LESS THAN MAXVALUE
);

-- 集群监控分钟维度聚合表
CREATE TABLE `cluster_monitor_minute_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `cluster_code` varchar(64) NOT NULL COMMENT '集群编码',
  `metric_type` varchar(32) NOT NULL COMMENT '指标类型：ceph_pressure,write_rate,capacity_usage等',
  `minute_time` datetime NOT NULL COMMENT '分钟时间点（精确到分钟）',
  `avg_value` double NOT NULL COMMENT '平均值',
  `max_value` double NOT NULL COMMENT '最大值',
  `min_value` double NOT NULL COMMENT '最小值',
  `data_count` int(11) NOT NULL DEFAULT '0' COMMENT '原始数据点数量',
  `metric_unit` varchar(16) DEFAULT NULL COMMENT '指标单位',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_cluster_metric_minute` (`cluster_code`, `metric_type`, `minute_time`),
  KEY `idx_cluster_metric_time` (`cluster_code`, `metric_type`, `minute_time`),
  KEY `idx_minute_time` (`minute_time`),
  KEY `idx_metric_type` (`metric_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='集群监控分钟维度聚合表（保留3个月）'
PARTITION BY RANGE (TO_DAYS(minute_time)) (
  PARTITION p202501 VALUES LESS THAN (TO_DAYS('2025-02-01')),
  PARTITION p202502 VALUES LESS THAN (TO_DAYS('2025-03-01')),
  PARTITION p202503 VALUES LESS THAN (TO_DAYS('2025-04-01')),
  PARTITION p202504 VALUES LESS THAN (TO_DAYS('2025-05-01')),
  PARTITION p202505 VALUES LESS THAN (TO_DAYS('2025-06-01')),
  PARTITION p202506 VALUES LESS THAN (TO_DAYS('2025-07-01')),
  PARTITION p202507 VALUES LESS THAN (TO_DAYS('2025-08-01')),
  PARTITION p202508 VALUES LESS THAN (TO_DAYS('2025-09-01')),
  PARTITION p202509 VALUES LESS THAN (TO_DAYS('2025-10-01')),
  PARTITION p202510 VALUES LESS THAN (TO_DAYS('2025-11-01')),
  PARTITION p202511 VALUES LESS THAN (TO_DAYS('2025-12-01')),
  PARTITION p202512 VALUES LESS THAN (TO_DAYS('2026-01-01')),
  PARTITION pmax VALUES LESS THAN MAXVALUE
);

-- 创建分钟聚合数据的存储过程（使用数据库函数优化）
DELIMITER $$
CREATE PROCEDURE `AggregateClusterMonitorData`(
    IN p_start_time DATETIME,
    IN p_end_time DATETIME
)
BEGIN
    DECLARE v_affected_rows INT DEFAULT 0;

    -- 使用数据库函数进行聚合，避免游标循环
    INSERT INTO cluster_monitor_minute_data (
        cluster_code,
        metric_type,
        minute_time,
        avg_value,
        max_value,
        min_value,
        data_count,
        metric_unit,
        create_time
    )
    SELECT
        cluster_code,
        metric_type,
        DATE_FORMAT(report_time, '%Y-%m-%d %H:%i:00') as minute_time,
        AVG(metric_value) as avg_value,
        MAX(metric_value) as max_value,
        MIN(metric_value) as min_value,
        COUNT(*) as data_count,
        MAX(metric_unit) as metric_unit,
        NOW() as create_time
    FROM cluster_monitor_raw_data
    WHERE report_time BETWEEN p_start_time AND p_end_time
    GROUP BY cluster_code, metric_type, DATE_FORMAT(report_time, '%Y-%m-%d %H:%i:00')
    ON DUPLICATE KEY UPDATE
        -- 使用数据库函数计算加权平均值
        avg_value = (VALUES(avg_value) * VALUES(data_count) + avg_value * data_count) / (VALUES(data_count) + data_count),
        -- 使用GREATEST函数更新最大值
        max_value = GREATEST(max_value, VALUES(max_value)),
        -- 使用LEAST函数更新最小值
        min_value = LEAST(min_value, VALUES(min_value)),
        -- 累加数据点数量
        data_count = data_count + VALUES(data_count),
        metric_unit = VALUES(metric_unit),
        update_time = NOW();

    -- 获取影响的行数
    SET v_affected_rows = ROW_COUNT();

    -- 记录聚合日志（可选）
    INSERT INTO cluster_monitor_log (
        operation_type,
        start_time,
        end_time,
        affected_rows,
        create_time
    ) VALUES (
        'AGGREGATE',
        p_start_time,
        p_end_time,
        v_affected_rows,
        NOW()
    );

    SELECT v_affected_rows as affected_rows;
END$$
DELIMITER ;

-- 创建聚合日志表（可选，用于监控聚合任务执行情况）
CREATE TABLE IF NOT EXISTS `cluster_monitor_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `operation_type` varchar(32) NOT NULL COMMENT '操作类型：AGGREGATE,CLEAN等',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `affected_rows` int(11) DEFAULT '0' COMMENT '影响行数',
  `error_message` text COMMENT '错误信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_operation_create_time` (`operation_type`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='集群监控操作日志表';

-- 创建定时调用聚合存储过程的事件（可选）
-- SET GLOBAL event_scheduler = ON;
-- CREATE EVENT IF NOT EXISTS `evt_aggregate_cluster_monitor_data`
-- ON SCHEDULE EVERY 1 MINUTE
-- DO
--   CALL AggregateClusterMonitorData(
--     DATE_SUB(NOW(), INTERVAL 2 MINUTE),
--     DATE_SUB(NOW(), INTERVAL 1 MINUTE)
--   );

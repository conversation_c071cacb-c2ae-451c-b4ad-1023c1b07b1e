package net.armcloud.paascenter.traffic.info.service;

import net.armcloud.paascenter.traffic.info.internal.dto.CephPressureReportDTO;
import net.armcloud.paascenter.traffic.info.mapper.paas.ClusterMonitorDataMapper;
import net.armcloud.paascenter.traffic.info.model.dto.CephPressureQueryDTO;
import net.armcloud.paascenter.traffic.info.model.dto.CephPressureChartDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Ceph压力数据服务测试类
 * 用于验证实时聚合功能
 *
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class CephPressureDataServiceTest {

    @Resource
    private ICephPressureDataService cephPressureDataService;

    @Resource
    private ClusterMonitorDataMapper clusterMonitorDataMapper;

    /**
     * 测试实时聚合功能
     */
    @Test
    public void testRealTimeAggregation() {
        // 准备测试数据
        CephPressureReportDTO reportDTO = new CephPressureReportDTO();
        reportDTO.setClusterCode("test-cluster-001");
        
        CephPressureReportDTO.Metrics metrics = new CephPressureReportDTO.Metrics();
        metrics.setCephPressure(25.5);
        reportDTO.setMetrics(metrics);

        // 执行数据上报（应该同时更新原始表和分钟聚合表）
        Boolean result = cephPressureDataService.saveCephPressureData(reportDTO);
        
        // 验证结果
        assert result != null && result;
        System.out.println("实时聚合测试通过：数据上报成功");

        // 再次上报相同分钟的数据，验证聚合逻辑
        metrics.setCephPressure(30.0);
        reportDTO.setMetrics(metrics);
        
        Boolean result2 = cephPressureDataService.saveCephPressureData(reportDTO);
        assert result2 != null && result2;
        System.out.println("实时聚合测试通过：相同分钟数据聚合成功");
    }

    /**
     * 测试查询功能
     */
    @Test
    public void testQueryChartData() {
        // 先插入一些测试数据
        testRealTimeAggregation();

        // 准备查询参数
        CephPressureQueryDTO queryDTO = new CephPressureQueryDTO();
        queryDTO.setClusterCode("test-cluster-001");
        queryDTO.setStartTime(new Date(System.currentTimeMillis() - 30 * 60 * 1000)); // 30分钟前
        queryDTO.setEndTime(new Date());

        // 执行查询
        List<CephPressureChartDTO> chartData = cephPressureDataService.getChartData(queryDTO);
        
        // 验证结果
        assert chartData != null;
        System.out.println("查询测试通过：返回数据条数 = " + chartData.size());
        
        if (!chartData.isEmpty()) {
            CephPressureChartDTO firstData = chartData.get(0);
            System.out.println("第一条数据：集群=" + firstData.getClusterCode() +
                             ", 时间=" + firstData.getTimeStr() +
                             ", 平均值=" + firstData.getAvgPressure() +
                             ", 最大值=" + firstData.getMaxPressure() +
                             ", 最小值=" + firstData.getMinPressure() +
                             ", 数据点数=" + firstData.getDataCount());
        }
    }

    /**
     * 测试批量数据上报的聚合效果
     */
    @Test
    public void testBatchReportAggregation() {
        String clusterCode = "test-cluster-batch";
        
        // 模拟同一分钟内的多次数据上报
        double[] pressureValues = {20.0, 25.0, 30.0, 22.0, 28.0};
        
        for (double pressure : pressureValues) {
            CephPressureReportDTO reportDTO = new CephPressureReportDTO();
            reportDTO.setClusterCode(clusterCode);
            
            CephPressureReportDTO.Metrics metrics = new CephPressureReportDTO.Metrics();
            metrics.setCephPressure(pressure);
            reportDTO.setMetrics(metrics);
            
            Boolean result = cephPressureDataService.saveCephPressureData(reportDTO);
            assert result != null && result;
            
            // 短暂延迟，确保在同一分钟内
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        System.out.println("批量聚合测试完成：上报了" + pressureValues.length + "条数据");
        
        // 验证聚合结果
        CephPressureQueryDTO queryDTO = new CephPressureQueryDTO();
        queryDTO.setClusterCode(clusterCode);
        queryDTO.setStartTime(new Date(System.currentTimeMillis() - 5 * 60 * 1000)); // 5分钟前
        queryDTO.setEndTime(new Date());
        
        List<CephPressureChartDTO> chartData = cephPressureDataService.getChartData(queryDTO);
        
        if (!chartData.isEmpty()) {
            CephPressureChartDTO aggregatedData = chartData.get(0);
            System.out.println("聚合结果验证：");
            System.out.println("  平均值=" + aggregatedData.getAvgPressure());
            System.out.println("  最大值=" + aggregatedData.getMaxPressure());
            System.out.println("  最小值=" + aggregatedData.getMinPressure());
            System.out.println("  数据点数=" + aggregatedData.getDataCount());
            
            // 验证聚合计算是否正确
            double expectedAvg = (20.0 + 25.0 + 30.0 + 22.0 + 28.0) / 5;
            double expectedMax = 30.0;
            double expectedMin = 20.0;
            int expectedCount = 5;
            
            assert Math.abs(aggregatedData.getAvgPressure() - expectedAvg) < 0.01;
            assert Math.abs(aggregatedData.getMaxPressure() - expectedMax) < 0.01;
            assert Math.abs(aggregatedData.getMinPressure() - expectedMin) < 0.01;
            assert aggregatedData.getDataCount() == expectedCount;
            
            System.out.println("批量聚合验证通过：计算结果正确");
        }
    }

    /**
     * 测试查询所有集群数据
     */
    @Test
    public void testQueryAllClustersData() {
        // 先插入多个集群的测试数据
        String[] clusterCodes = {"cluster-001", "cluster-002", "cluster-003"};

        for (String clusterCode : clusterCodes) {
            CephPressureReportDTO reportDTO = new CephPressureReportDTO();
            reportDTO.setClusterCode(clusterCode);

            CephPressureReportDTO.Metrics metrics = new CephPressureReportDTO.Metrics();
            metrics.setCephPressure(20.0 + Math.random() * 20); // 20-40之间的随机值
            reportDTO.setMetrics(metrics);

            Boolean result = cephPressureDataService.saveCephPressureData(reportDTO);
            assert result != null && result;
        }

        // 查询所有集群数据（不指定集群编码）
        CephPressureQueryDTO queryDTO = new CephPressureQueryDTO();
        queryDTO.setClusterCode(null); // 不指定集群编码
        queryDTO.setStartTime(new Date(System.currentTimeMillis() - 5 * 60 * 1000)); // 5分钟前
        queryDTO.setEndTime(new Date());

        List<CephPressureChartDTO> allClustersData = cephPressureDataService.getChartData(queryDTO);

        System.out.println("查询所有集群数据：返回数据条数 = " + allClustersData.size());

        // 验证返回的数据包含多个集群
        Set<String> returnedClusters = allClustersData.stream()
                .map(CephPressureChartDTO::getClusterCode)
                .collect(Collectors.toSet());

        System.out.println("返回的集群列表：" + returnedClusters);
        assert returnedClusters.size() >= 3; // 至少包含3个集群

        // 查询单个集群数据
        queryDTO.setClusterCode("cluster-001");
        List<CephPressureChartDTO> singleClusterData = cephPressureDataService.getChartData(queryDTO);

        System.out.println("查询单个集群数据：返回数据条数 = " + singleClusterData.size());

        // 验证单个集群查询结果
        for (CephPressureChartDTO data : singleClusterData) {
            assert "cluster-001".equals(data.getClusterCode());
        }

        System.out.println("多集群查询测试通过");
    }
}

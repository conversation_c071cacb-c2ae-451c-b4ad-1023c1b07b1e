-- 集群监控原始数据表（支持多种指标）- 更新版本
-- 去除分区表配置，使用普通表
CREATE TABLE `cluster_monitor_raw_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `cluster_code` varchar(64) NOT NULL COMMENT '集群编码',
  `metric_type` varchar(32) NOT NULL COMMENT '指标类型：ceph_pressure,write_rate,capacity_usage等',
  `metric_value` double NOT NULL COMMENT '指标值',
  `metric_unit` varchar(16) DEFAULT NULL COMMENT '指标单位：%,MB/s,GB等',
  `report_time` datetime NOT NULL COMMENT '上报时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT 'system' COMMENT '创建者',
  PRIMARY KEY (`id`),
  KEY `idx_cluster_metric_time` (`cluster_code`, `metric_type`, `report_time`),
  KEY `idx_report_time` (`report_time`),
  KEY `idx_metric_type` (`metric_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='集群监控原始数据表';

-- 集群监控分钟维度聚合表（去除分区表配置）
CREATE TABLE `cluster_monitor_minute_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `cluster_code` varchar(64) NOT NULL COMMENT '集群编码',
  `metric_type` varchar(32) NOT NULL COMMENT '指标类型：ceph_pressure,write_rate,capacity_usage等',
  `minute_time` datetime NOT NULL COMMENT '分钟时间点（精确到分钟）',
  `avg_value` double NOT NULL COMMENT '平均值',
  `max_value` double NOT NULL COMMENT '最大值',
  `min_value` double NOT NULL COMMENT '最小值',
  `data_count` int(11) NOT NULL DEFAULT '0' COMMENT '原始数据点数量',
  `metric_unit` varchar(16) DEFAULT NULL COMMENT '指标单位',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_cluster_metric_minute` (`cluster_code`, `metric_type`, `minute_time`),
  KEY `idx_cluster_metric_time` (`cluster_code`, `metric_type`, `minute_time`),
  KEY `idx_minute_time` (`minute_time`),
  KEY `idx_metric_type` (`metric_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='集群监控分钟维度聚合表';

-- 创建聚合日志表（用于监控聚合任务执行情况）
CREATE TABLE IF NOT EXISTS `cluster_monitor_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `operation_type` varchar(32) NOT NULL COMMENT '操作类型：AGGREGATE,CLEAN等',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `affected_rows` int(11) DEFAULT '0' COMMENT '影响行数',
  `error_message` text COMMENT '错误信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_operation_create_time` (`operation_type`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='集群监控操作日志表';

-- 插入测试数据（可选）
INSERT INTO cluster_monitor_raw_data (cluster_code, metric_type, metric_value, metric_unit, report_time, create_by) VALUES
('cluster001', 'ceph_pressure', 25.5, '%', NOW(), 'system'),
('cluster001', 'ceph_pressure', 28.3, '%', DATE_ADD(NOW(), INTERVAL 1 MINUTE), 'system'),
('cluster002', 'ceph_pressure', 15.2, '%', NOW(), 'system');

-- 说明：
-- 1. 已去除存储过程定义，改为Java代码实现聚合逻辑
-- 2. 去除分区表配置，使用普通表结构
-- 3. 保留必要的索引以确保查询性能
-- 4. 实时聚合逻辑通过应用层实现，在数据上报时同步更新分钟维度表
-- 5. 定时任务仍可用于补充聚合和数据清理，但主要依赖实时聚合

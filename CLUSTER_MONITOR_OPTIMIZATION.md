# 集群指标数据上报聚合逻辑优化

## 优化概述

根据需求，对集群指标数据上报聚合逻辑进行了以下三个主要调整：

1. **去除存储过程的使用**
2. **实时更新分钟维度表数据**
3. **使用普通表替代分区表**

## 详细调整内容

### 1. 去除存储过程的使用

#### 调整前
- 使用MySQL存储过程 `AggregateClusterMonitorData` 进行数据聚合
- 在XML映射文件中通过 `callAggregateStoredProcedure` 方法调用存储过程

#### 调整后
- 完全移除存储过程调用相关代码
- 使用纯Java代码配合SQL语句实现聚合逻辑
- 更新了 `ClusterMonitorDataMapper.xml` 中的相关注释和方法

#### 涉及文件
- `src/main/java/net/armcloud/paascenter/traffic/info/mapper/paas/ClusterMonitorDataMapper.java`
- `src/main/resources/mapper/paas/CephPressureDataMapper.xml`
- `src/main/java/net/armcloud/paascenter/traffic/info/service/impl/ClusterMonitorAggregationServiceImpl.java`

### 2. 实时更新分钟维度表数据

#### 调整前
- 通过定时任务 `ClusterMonitorDataTask` 每分钟执行聚合
- 数据上报和聚合是异步分离的

#### 调整后
- 在数据上报时同步更新分钟维度表
- 新增 `aggregateSingleDataToMinute` 方法实现实时聚合
- 禁用了定时聚合任务（保留代码以备需要时启用）

#### 核心实现
```java
// 在 CephPressureDataServiceImpl.saveCephPressureData 方法中
// 1. 保存原始数据
int result = clusterMonitorDataMapper.insert(data);

// 2. 实时更新分钟维度表
if (result > 0) {
    int aggregateResult = clusterMonitorDataMapper.aggregateSingleDataToMinute(
        reportDTO.getClusterCode(),
        MetricTypeConstant.CEPH_PRESSURE,
        reportDTO.getMetrics().getCephPressure(),
        "%",
        reportTime
    );
}
```

#### 涉及文件
- `src/main/java/net/armcloud/paascenter/traffic/info/service/impl/CephPressureDataServiceImpl.java`
- `src/main/java/net/armcloud/paascenter/traffic/info/task/ClusterMonitorDataTask.java`
- `src/main/resources/mapper/paas/CephPressureDataMapper.xml`

### 3. 使用普通表替代分区表

#### 调整前
- `cluster_monitor_minute_data` 表使用分区表结构
- 按月份进行分区（p202501, p202502等）

#### 调整后
- 去除分区表配置，使用普通表结构
- 保留必要的索引以确保查询性能
- 创建了新的SQL文件 `sql/cluster_monitor_data_updated.sql`

#### 表结构对比
```sql
-- 调整前（分区表）
PARTITION BY RANGE (TO_DAYS(minute_time)) (
  PARTITION p202501 VALUES LESS THAN (TO_DAYS('2025-02-01')),
  ...
);

-- 调整后（普通表）
KEY `idx_cluster_metric_time` (`cluster_code`, `metric_type`, `minute_time`),
KEY `idx_minute_time` (`minute_time`),
KEY `idx_metric_type` (`metric_type`)
```

## 新增功能

### 实时聚合SQL
在XML映射文件中新增了 `aggregateSingleDataToMinute` 方法的SQL实现：

```sql
INSERT INTO cluster_monitor_minute_data (
    cluster_code, metric_type, minute_time, avg_value, max_value, min_value, data_count, metric_unit, create_time
) VALUES (
    #{clusterCode}, #{metricType}, DATE_FORMAT(#{reportTime}, '%Y-%m-%d %H:%i:00'),
    #{metricValue}, #{metricValue}, #{metricValue}, 1, #{metricUnit}, NOW()
) ON DUPLICATE KEY UPDATE
    avg_value = (avg_value * data_count + #{metricValue}) / (data_count + 1),
    max_value = GREATEST(max_value, #{metricValue}),
    min_value = LEAST(min_value, #{metricValue}),
    data_count = data_count + 1,
    metric_unit = #{metricUnit},
    update_time = NOW()
```

## 优化效果

1. **性能提升**：去除存储过程调用，减少数据库往返次数
2. **实时性增强**：数据上报即时反映在分钟维度表中
3. **维护简化**：普通表结构更易于维护和扩展
4. **代码可控**：聚合逻辑完全由Java代码控制，便于调试和优化

## 注意事项

1. **定时任务保留**：虽然禁用了定时聚合任务，但代码仍保留，可在需要时启用用于数据补充
2. **事务处理**：实时聚合在同一事务中进行，确保数据一致性
3. **错误处理**：增加了详细的日志记录，便于问题排查
4. **索引优化**：普通表结构中保留了必要的索引，确保查询性能

## 部署建议

1. 先执行新的SQL脚本创建表结构
2. 部署更新后的应用代码
3. 验证实时聚合功能正常工作
4. 可选择性启用定时任务作为数据补充机制
